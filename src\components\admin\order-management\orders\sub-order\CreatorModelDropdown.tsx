import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { CreatorInterface } from '@/types/interfaces';
import { X, Search, User } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@/store/store';
import { assignCreatorsToOrder, fetchOrderById } from '@/store/features/admin/ordersSlice';
import { toast } from 'react-toastify';
// import Order from '@/app/admin/order-management/brands/page';

interface CreatorModelDropdownProps {
    creatorModel: boolean;
    appliedCreators: CreatorInterface[];
    onClose: () => void;
    onSelectCreators: (selectedCreators: CreatorInterface[]) => void;
    currentlyAssigned?: CreatorInterface[];
    currentOrder?:any
}

const CreatorModelDropdown = ({
    creatorModel,
    appliedCreators = [],
    onClose,
    onSelectCreators,
    currentlyAssigned = [],
    currentOrder,
}: CreatorModelDropdownProps) => {
    const [selectedCreators, setSelectedCreators] = useState<CreatorInterface[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const dispatch = useDispatch<AppDispatch>();
    const [isLoading, setIsLoading] = useState(false);

    const handleSave = async () => {
        console.log("💾 Saving selected creators:", selectedCreators);

        // Show loading state
        setIsLoading(true);

        try {
            // Extract creator IDs
            const creatorIds = selectedCreators.map(creator => creator._id);

            console.log("🚀 Sending creator IDs to API:", creatorIds);

            // Call API to assign creators
            await dispatch(assignCreatorsToOrder({
                orderId: currentOrder._id,
                assignedCreators: creatorIds
            })).unwrap();

            console.log("✅ API call successful");

            // Show success message
            toast.success("Creators has been assigned to order");

            // Update the form with the selected creators (full objects, not IDs)
            console.log("📝 Updating form with selected creators:", selectedCreators);
            onSelectCreators(selectedCreators);

            // Optional: Refresh the order data to ensure consistency
            // Uncomment this if the direct Redux update doesn't work perfectly
            // console.log("🔄 Refreshing order data for consistency");
            // await dispatch(fetchOrderById({ orderId: currentOrder._id }));

            // Close modal
            onClose();

        } catch (error: any) {
            console.error('❌ Error assigning creators:', error);
            toast.error(error.message || "Failed to assign creators");
        } finally {
            setIsLoading(false);
        }
    };

    // Initialize selected creators when modal opens
    React.useEffect(() => {
        console.log("� DEBUGGING CREATOR MODAL:");
        console.log("Modal opened:", creatorModel);
        console.log("Applied creators:", appliedCreators);
        console.log("Applied creators type:", typeof appliedCreators);
        console.log("Applied creators is array:", Array.isArray(appliedCreators));
        console.log("Applied creators length:", appliedCreators?.length);
        console.log("Currently assigned:", currentlyAssigned);
        console.log("Currently assigned length:", currentlyAssigned?.length);

        if (creatorModel) {
            // If no applied creators, use currently assigned as fallback
            if (!appliedCreators || appliedCreators.length === 0) {
                console.log("⚠️ No applied creators found, using assigned creators as fallback");
                if (currentlyAssigned && currentlyAssigned.length > 0) {
                    setSelectedCreators(currentlyAssigned);
                }
            } else {
                if (currentlyAssigned && currentlyAssigned.length > 0) {
                    console.log("✅ Setting currently assigned creators:", currentlyAssigned);
                    setSelectedCreators(currentlyAssigned);
                } else {
                    console.log("⚠️ No currently assigned creators, resetting selection");
                    setSelectedCreators([]);
                }
            }
        }
    }, [creatorModel, currentlyAssigned, appliedCreators]);

    // Use applied creators, or fallback to assigned creators if no applied creators exist
    const creatorsToShow = appliedCreators && appliedCreators.length > 0
        ? appliedCreators
        : currentlyAssigned || [];

    console.log("🎯 Creators to show:", creatorsToShow);
    console.log("🎯 Creators to show length:", creatorsToShow.length);

    // Filter creators based on search term
    const filteredCreators = creatorsToShow.filter(creator => {
        if (!creator) return false;

        // If no search term, show all creators
        if (!searchTerm || searchTerm.trim() === '') {
            return true;
        }

        const searchLower = searchTerm.toLowerCase();
        const fullName = creator.fullName || (creator as any).name || '';
        const email = creator.email || '';

        console.log("🔍 Filtering creator:", creator);
        console.log("🔍 Creator fullName:", fullName);
        console.log("🔍 Creator email:", email);
        console.log("🔍 Search term:", searchTerm);

        const matches = fullName.toLowerCase().includes(searchLower) ||
                       email.toLowerCase().includes(searchLower);

        console.log("🔍 Creator matches:", matches);
        return matches;
    });

    console.log("🎯 Final filtered creators:", filteredCreators);
    console.log("🎯 Final filtered creators length:", filteredCreators.length);

    // Handle creator selection/deselection
    const handleCreatorToggle = (creator: CreatorInterface) => {
        console.log("🔄 Toggling creator:", creator.fullName);
        setSelectedCreators(prev => {
            const isSelected = prev.some(c => c._id === creator._id);
            console.log("📊 Is currently selected:", isSelected);

            if (isSelected) {
                const newSelection = prev.filter(c => c._id !== creator._id);
                console.log("➖ Removing creator, new selection:", newSelection.map(c => c.fullName));
                return newSelection;
            } else {
                const newSelection = [...prev, creator];
                console.log("➕ Adding creator, new selection:", newSelection.map(c => c.fullName));
                return newSelection;
            }
        });
    };

    // Check if creator is selected
    const isCreatorSelected = (creator: CreatorInterface) => {
        return selectedCreators.some(c => c._id === creator._id);
    };

    // Handle save and close
    // const handleSave = () => {
    //     console.log("💾 Saving selected creators:", selectedCreators);
    //     onSelectCreators(selectedCreators);
    //     onClose();
    // };

    // Handle select all
    const handleSelectAll = () => {
        setSelectedCreators([...creatorsToShow]);
    };

    // Handle clear all
    const handleClearAll = () => {
        setSelectedCreators([]);
    };

    if (!creatorModel) return null;

    const modalContent = (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-800">
                            Select Creators
                        </h2>
                        <p className="text-sm text-gray-600 mt-1">
                            {selectedCreators.length} of {creatorsToShow.length} creators selected
                        </p>
                        {selectedCreators.length > 0 && (
                            <p className="text-xs text-blue-600 mt-1">
                                Selected: {selectedCreators.map(c => c.fullName).join(", ")}
                            </p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                            {appliedCreators.length > 0 ? "Showing applied creators" : "Showing assigned creators (no applications)"}
                        </p>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-white hover:shadow-md rounded-full transition-all duration-200"
                    >
                        <X size={24} className="text-gray-600" />
                    </button>
                </div>

                {/* Search */}
                <div className="p-6 border-b bg-gray-50">
                    <div className="relative">
                        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                        <input
                            type="text"
                            placeholder="Search creators by name or email..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                        />
                    </div>
                    <div className="flex items-center justify-between mt-3">
                        {creatorsToShow.length > 0 && (
                            <p className="text-sm text-gray-500">
                                {filteredCreators.length} creator{filteredCreators.length !== 1 ? 's' : ''} found
                            </p>
                        )}
                        <div className="flex space-x-2">
                            <button
                                onClick={handleSelectAll}
                                className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                            >
                                Select All
                            </button>
                            <button
                                onClick={handleClearAll}
                                className="text-xs px-3 py-1 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                            >
                                Clear All
                            </button>
                        </div>
                    </div>
                </div>

                {/* Creator List */}
                <div className="flex-1 overflow-y-auto max-h-96">
                    {creatorsToShow.length === 0 ? (
                        <div className="p-8 text-center text-gray-500">
                            <User size={48} className="mx-auto mb-4 text-gray-300" />
                            <p>No creators found</p>
                            <p className="text-sm mt-2">No applied or assigned creators available</p>
                        </div>
                    ) : filteredCreators.length === 0 ? (
                        <div className="p-8 text-center text-gray-500">
                            <User size={48} className="mx-auto mb-4 text-gray-300" />
                            <p>No creators found</p>
                            {searchTerm && (
                                <p className="text-sm mt-2">Try adjusting your search terms</p>
                            )}
                        </div>
                    ) : (
                        <div className="p-6 space-y-3">
                            {filteredCreators.map((creator) => (
                                <div
                                    key={creator._id}
                                    className={`flex items-center px-3 py-1 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                                        isCreatorSelected(creator)
                                            ? 'bg-blue-50 border-blue-300 shadow-md transform scale-[1.02]'
                                            : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm'
                                    }`}
                                    onClick={() => handleCreatorToggle(creator)}
                                >
                                    <input
                                        type="checkbox"
                                        checked={isCreatorSelected(creator)}
                                        onChange={() => handleCreatorToggle(creator)}
                                        className="mr-4 h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <div className="flex-1">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <h3 className="font-semibold text-gray-900 text-lg">
                                                    {creator.fullName || (creator as any).name || 'Unknown Creator'}
                                                </h3>
                                                <p className="text-sm text-gray-600 mt-1">
                                                     {creator.email || 'No email'}
                                                </p>
                                                {creator.phoneNumber && (
                                                    <p className="text-sm text-gray-500 mt-1">
                                                        📱 {creator.phoneNumber}
                                                    </p>
                                                )}
                                                {/* <p className="text-xs text-gray-400 mt-1">
                                                    🆔 {creator._id || 'No ID'}
                                                </p> */}
                                            </div>
                                            <div className="text-right ml-4">
                                                {/* <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full">
                                                    ID: {creator._id}
                                                </span> */}
                                                <div className="mt-2 space-y-1">
                                                    {currentlyAssigned.some(c => c._id === creator._id) && (
                                                        <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full font-medium block">
                                                             Currently Assigned
                                                        </span>
                                                    )}
                                                    {isCreatorSelected(creator) && (
                                                        <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full font-medium block">
                                                            ✓ Selected
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between p-6 border-t bg-gradient-to-r from-gray-50 to-gray-100">
                    <div className="text-sm font-medium text-gray-700">
                        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                            {selectedCreators.length} selected
                        </span>
                        <span className="ml-2 text-gray-500">
                            of {creatorsToShow.length} total
                        </span>
                    </div>
                    <div className="flex space-x-3">
                        <button
                            onClick={onClose}
                            className="px-6 py-2 text-gray-700 bg-white border-2 border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleSave}
                            disabled={isLoading}
                            className={`px-6 py-2 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl ${
                                isLoading
                                    ? 'bg-gray-400 cursor-not-allowed'
                                    : 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800'
                            }`}
                        >
                            {isLoading ? (
                                <div className="flex items-center">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Saving...
                                </div>
                            ) : (
                                'Save Selection'
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );

    // Use portal to render modal at document body level for proper positioning
    return typeof document !== 'undefined'
        ? createPortal(modalContent, document.body)
        : modalContent;
};

export default CreatorModelDropdown;
