"use client";

import { useEffect } from "react";
import "../i18n";
import { I18nextProvider } from "react-i18next";
import i18n from "../i18n";
import "../app/globals.css";

import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "@/store/store";

import { ToastContainer } from "react-toastify";
import LoadingSpinner from "@/components/loaders/LoadingSpinner";
import { FileProvider } from "@/context/FileContext";
import { TokenProvider } from "@/context/TokenCheckingContext";
import { initGA } from "@/utils/googleAnalytics/Analytics";
import RouteChangeTracker from "@/utils/googleAnalytics/RouteChangeTracker";
import Hotjar from '@hotjar/browser';
import { initMixpanel } from "@/utils/mixpanel/mixpanelUtils";
import AuthWrapper from "@/components/auth/AuthWrapper";
import LayoutWrapper from "@/components/layout/LayoutWrapper";

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    useEffect(() => {
        // Initialize Hotjar
        try {
            const hotjarId = process.env.NEXT_PUBLIC_HOTJAR_ID;
            const hotjarVersion = process.env.NEXT_PUBLIC_HOTJAR_VERSION;

            console.log("🔥 Starting Hotjar initialization...");
            console.log("Hotjar ID:", hotjarId);
            console.log("Hotjar Version:", hotjarVersion);

            if (!hotjarId || !hotjarVersion) {
                console.warn("⚠️ Hotjar environment variables are missing!");
                console.log("NEXT_PUBLIC_HOTJAR_ID:", hotjarId);
                console.log("NEXT_PUBLIC_HOTJAR_VERSION:", hotjarVersion);
                return;
            }

            // Convert to numbers
            const siteId = parseInt(hotjarId);
            const version = parseInt(hotjarVersion);

            console.log("🔥 Initializing Hotjar with ID:", siteId, "Version:", version);

            Hotjar.init(siteId, version);

            // Check if Hotjar was successfully initialized
            setTimeout(() => {
                if (typeof window !== 'undefined' && (window as any).hj) {
                    console.log("✅ Hotjar successfully initialized!");
                    console.log("Hotjar object:", (window as any).hj);
                } else {
                    console.error("❌ Hotjar failed to initialize - hj object not found on window");
                }
            }, 1000);

        } catch (error) {
            console.error("❌ Failed to initialize Hotjar:", error);
        }

        // Initialize Google Analytics
        try {
            initGA();
        } catch (error) {
            console.error("Failed to initialize Google Analytics:", error);
        }
    }, []);
    useEffect(() => {
        // Initialize Mixpanel with improved error handling
        try {
            initMixpanel();
        } catch (error) {
            console.error("Failed to initialize Mixpanel:", error);
        }
    }, []);

    // Set favicon and meta tags dynamically in client component
  useEffect(() => {
  const setMetaAndFavicon = () => {
    // Set document title
    document.title = 'Contentia';

    const timestamp = Date.now();
    const faviconUrl = `/Favicon_Contentia.png?v=${timestamp}`;

    // Set or update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.setAttribute('name', 'description');
      document.head.appendChild(metaDescription);
    }
    metaDescription.setAttribute('content', 'Contentia');

    // Set or update viewport meta
    let metaViewport = document.querySelector('meta[name="viewport"]');
    if (!metaViewport) {
      metaViewport = document.createElement('meta');
      metaViewport.setAttribute('name', 'viewport');
      document.head.appendChild(metaViewport);
    }
    metaViewport.setAttribute('content', 'width=device-width, initial-scale=1');

    // Set or update Google site verification
    let googleVerification = document.querySelector('meta[name="google-site-verification"]');
    if (!googleVerification) {
      googleVerification = document.createElement('meta');
      googleVerification.setAttribute('name', 'google-site-verification');
      document.head.appendChild(googleVerification);
    }
    googleVerification.setAttribute('content', 'RR_iYELjo0fmU-N4xawWBZYH16e23B5xuvZT73WQl6M');

    // Remove existing favicons
    document.querySelectorAll('link[rel*="icon"]').forEach(link => link.remove());

    // Helper to create favicon link elements
    const createFaviconLink = (rel: string, sizes: string | null = null) => {
      const link = document.createElement('link');
      link.setAttribute('rel', rel);
      link.setAttribute('type', 'image/png');
      if (sizes) link.setAttribute('sizes', sizes);
      link.setAttribute('href', faviconUrl);
      document.head.appendChild(link);
    };

    // Create favicons
    createFaviconLink('icon', '16x16');
    createFaviconLink('icon', '32x32');
    createFaviconLink('apple-touch-icon', '180x180');
    createFaviconLink('shortcut icon');
  };

  setMetaAndFavicon();
}, []);






    return (
        
        <html lang='en'>



            <body suppressHydrationWarning={true}>
                <FileProvider>
                    <Provider store={store}>
                        <PersistGate
                            loading={<LoadingSpinner />}
                            persistor={persistor}
                            onBeforeLift={() => {
                                console.log("🔄 PersistGate: Store hydration completed successfully");
                            }}
                        >
                            <TokenProvider>
                                <I18nextProvider i18n={i18n}>
                                    <RouteChangeTracker />

                                    <AuthWrapper>
                                        <LayoutWrapper>{children}</LayoutWrapper>
                                    </AuthWrapper>

                                    <ToastContainer />
                                </I18nextProvider>
                            </TokenProvider>
                        </PersistGate>
                    </Provider>
                </FileProvider>
            </body>
        </html>
    );
}
