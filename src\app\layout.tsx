"use client";

import { useEffect } from "react";
import "../i18n";
import { I18nextProvider } from "react-i18next";
import i18n from "../i18n";
import "../app/globals.css";

import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "@/store/store";

import { ToastContainer } from "react-toastify";
import LoadingSpinner from "@/components/loaders/LoadingSpinner";
import { FileProvider } from "@/context/FileContext";
import { TokenProvider } from "@/context/TokenCheckingContext";
import { initGA } from "@/utils/googleAnalytics/Analytics";
import RouteChangeTracker from "@/utils/googleAnalytics/RouteChangeTracker";
import Hotjar from '@hotjar/browser';
import { initMixpanel } from "@/utils/mixpanel/mixpanelUtils";
import AuthWrapper from "@/components/auth/AuthWrapper";
import LayoutWrapper from "@/components/layout/LayoutWrapper";

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    useEffect(() => {
        // Initialize Hotjar
        try {
            const hotjarId = process.env.NEXT_PUBLIC_HOTJAR_ID;
            const hotjarVersion = process.env.NEXT_PUBLIC_HOTJAR_VERSION;

            console.log("🔥 Starting Hotjar initialization...");
            console.log("Hotjar ID:", hotjarId);
            console.log("Hotjar Version:", hotjarVersion);

            if (!hotjarId || !hotjarVersion) {
                console.warn("⚠️ Hotjar environment variables are missing!");
                console.log("NEXT_PUBLIC_HOTJAR_ID:", hotjarId);
                console.log("NEXT_PUBLIC_HOTJAR_VERSION:", hotjarVersion);
                return;
            }

            // Convert to numbers
            const siteId = parseInt(hotjarId);
            const version = parseInt(hotjarVersion);

            console.log("🔥 Initializing Hotjar with ID:", siteId, "Version:", version);

            Hotjar.init(siteId, version);

            // Check if Hotjar was successfully initialized
            setTimeout(() => {
                if (typeof window !== 'undefined' && (window as any).hj) {
                    console.log("✅ Hotjar successfully initialized!");
                    console.log("Hotjar object:", (window as any).hj);
                } else {
                    console.error("❌ Hotjar failed to initialize - hj object not found on window");
                }
            }, 1000);

        } catch (error) {
            console.error("❌ Failed to initialize Hotjar:", error);
        }

        // Initialize Google Analytics
        try {
            initGA();
        } catch (error) {
            console.error("Failed to initialize Google Analytics:", error);
        }
    }, []);
    useEffect(() => {
        // Initialize Mixpanel with improved error handling
        try {
            initMixpanel();
        } catch (error) {
            console.error("Failed to initialize Mixpanel:", error);
        }
    }, []);

    // Set favicon and meta tags dynamically in client component
    useEffect(() => {
        const setMetaAndFavicon = () => {
            // Set document title
            document.title = 'Contentia';

            // Set meta description
            let metaDescription = document.querySelector('meta[name="description"]');
            if (!metaDescription) {
                metaDescription = document.createElement('meta');
                metaDescription.setAttribute('name', 'description');
                document.head.appendChild(metaDescription);
            }
            metaDescription.setAttribute('content', 'Contentia');

            // Set viewport
            let metaViewport = document.querySelector('meta[name="viewport"]');
            if (!metaViewport) {
                metaViewport = document.createElement('meta');
                metaViewport.setAttribute('name', 'viewport');
                document.head.appendChild(metaViewport);
            }
            metaViewport.setAttribute('content', 'width=device-width, initial-scale=1');

            // Set Google verification
            let googleVerification = document.querySelector('meta[name="google-site-verification"]');
            if (!googleVerification) {
                googleVerification = document.createElement('meta');
                googleVerification.setAttribute('name', 'google-site-verification');
                document.head.appendChild(googleVerification);
            }
            googleVerification.setAttribute('content', 'RR_iYELjo0fmU-N4xawWBZYH16e23B5xuvZT73WQl6M');

            // Remove existing favicon links
            const existingLinks = document.querySelectorAll('link[rel*="icon"]');
            existingLinks.forEach(link => link.remove());

            // Create new favicon links with your new favicon
            const timestamp = Date.now();

            // Standard favicon
            const favicon16 = document.createElement('link');
            favicon16.rel = 'icon';
            favicon16.type = 'image/png';
            favicon16.sizes = '16x16';
            favicon16.href = `/Favicon_Contentia.png?v=${timestamp}`;
            document.head.appendChild(favicon16);

            // 32x32 favicon
            const favicon32 = document.createElement('link');
            favicon32.rel = 'icon';
            favicon32.type = 'image/png';
            favicon32.sizes = '32x32';
            favicon32.href = `/Favicon_Contentia.png?v=${timestamp}`;
            document.head.appendChild(favicon32);

            // Apple touch icon
            const appleIcon = document.createElement('link');
            appleIcon.rel = 'apple-touch-icon';
            appleIcon.sizes = '180x180';
            appleIcon.href = `/Favicon_Contentia.png?v=${timestamp}`;
            document.head.appendChild(appleIcon);

            // Shortcut icon
            const shortcutIcon = document.createElement('link');
            shortcutIcon.rel = 'shortcut icon';
            shortcutIcon.href = `/Favicon_Contentia.png?v=${timestamp}`;
            document.head.appendChild(shortcutIcon);

            console.log('✅ Meta tags and favicon updated successfully');
            console.log('📄 Title:', document.title);
            console.log('🎯 Favicon:', `/Favicon_Contentia.png?v=${timestamp}`);
        };

        setMetaAndFavicon();
    }, []);






    return (
        
        <html lang='en'>



            <body suppressHydrationWarning={true}>
                <FileProvider>
                    <Provider store={store}>
                        <PersistGate
                            loading={<LoadingSpinner />}
                            persistor={persistor}
                            onBeforeLift={() => {
                                console.log("🔄 PersistGate: Store hydration completed successfully");
                            }}
                        >
                            <TokenProvider>
                                <I18nextProvider i18n={i18n}>
                                    <RouteChangeTracker />

                                    <AuthWrapper>
                                        <LayoutWrapper>{children}</LayoutWrapper>
                                    </AuthWrapper>

                                    <ToastContainer />
                                </I18nextProvider>
                            </TokenProvider>
                        </PersistGate>
                    </Provider>
                </FileProvider>
            </body>
        </html>
    );
}
