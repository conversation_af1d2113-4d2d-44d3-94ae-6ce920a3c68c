// components/orders/ordercompletion/OrderSuccess.tsx
'use client';
import { useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { CheckCircle2 } from 'lucide-react';
import { toast } from 'react-toastify';
import { createInvoice } from '@/store/features/profile/orderSlice';
import { AppDispatch } from '@/store/store';

interface OrderSuccessProps {
  order_id: string;
}

export default function OrderSuccess({ order_id }: OrderSuccessProps) {
  const dispatch = useDispatch<AppDispatch>();

  const handleCreateInvoice = useCallback(async (orderId: string) => {
    try {
      const result = await dispatch(createInvoice({ orderId })).unwrap();
      console.log('✅ Invoice created successfully for order:', orderId);

      // Check if there was a warning (timeout but successful creation)
      if (result.warning) {
        console.warn('⚠️ Invoice creation warning:', result.warning);
        toast.success("Fatura başarıyla oluşturuldu! 🎉");
        toast.info("E-arşiv işlemi zaman aşımına uğradı ancak fatura oluşturuldu.", {
          autoClose: 10000,
          position: "top-center"
        });
      } else {
        toast.success("Fatura başarıyla oluşturuldu! 🎉");
      }
    } catch (error: any) {
      console.error('❌ Invoice creation error:', error);

      // Check if this is a timeout error
      if (error?.isTimeout) {
        console.warn('⚠️ Invoice creation timed out');
        toast.warning("Fatura oluşturma işlemi zaman aşımına uğradı. Lütfen fatura durumunu kontrol edin.", {
          autoClose: 12000,
          position: "top-center"
        });
        return;
      }

      // Check if the error actually contains successful invoice data
      if (error?.invoiceInfo || error?.data?.invoiceInfo ||
          (typeof error === 'string' && error.includes('e_archives monitoring timed out'))) {
        console.log('✅ Invoice was created despite error response');
        toast.success("Fatura başarıyla oluşturuldu! 🎉");
        toast.info("E-arşiv işlemi zaman aşımına uğradı ancak fatura oluşturuldu.", {
          autoClose: 10000,
          position: "top-center"
        });
      } else {
        // Only show error if we're sure the invoice wasn't created
        const errorMessage = error?.message || error || "Fatura oluşturulurken bir hata oluştu.";
        console.error('💥 Actual invoice creation failure:', errorMessage);
        toast.error(`Fatura oluşturulamadı: ${errorMessage}`);
      }
    }
  }, [dispatch]);

  useEffect(() => {
    if (order_id) {
      handleCreateInvoice(order_id);
    }
  }, [order_id, handleCreateInvoice]);
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white p-6">
      <div className="bg-white rounded-2xl shadow-xl p-8 max-w-lg w-full text-center">
        <div className="flex justify-center mb-4">
          <CheckCircle2 className="text-[#4d4ec9] w-16 h-16" />
        </div>

        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Ödeme Başarılı 🎉
        </h2>
        <p className="text-gray-600 mb-6">
          Siparişiniz başarıyla oluşturuldu. Teşekkür ederiz!
        </p>

        <div className="bg-gray-100 p-4 rounded-md text-left mb-6">
          <p className="text-sm text-gray-500">Sipariş Numaranız:</p>
          <p className="text-lg font-semibold text-gray-700">{order_id}</p>
        </div>

        <a
          href="/"
          className="inline-block bg-[#4d4ec9] text-white font-semibold px-6 py-3 rounded-md hover:bg-[#3f40a8] transition duration-200"

        >
          Ana Sayfaya Dön
        </a>
      </div>
    </div>
  );
}
