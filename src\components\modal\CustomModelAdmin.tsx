import {
    Dialog,
    DialogPanel,
    DialogTitle,
    Transition,
    TransitionChild,
} from "@headlessui/react";
import { Fragment } from "react";
import Image from "next/image";

interface ModalProps {
    isOpen: boolean;
    closeModal: () => void;
    title: string | React.ReactNode;
    children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({
    isOpen,
    closeModal,
    title,
    children,
}) => {
    return (
        <Transition
            appear
            show={isOpen}
            as={Fragment}
        >
            <Dialog
                as='div'
                className='relative z-50'
                onClose={closeModal}
            >
                <TransitionChild
                    as={Fragment}
                    enter='ease-out duration-300'
                    enterFrom='opacity-0'
                    enterTo='opacity-100'
                    leave='ease-in duration-200'
                    leaveFrom='opacity-100'
                    leaveTo='opacity-0'
                >
                    <div className='fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm' />
                </TransitionChild>

                <div className='fixed inset-0 overflow-y-auto'>
                    <div className='flex min-h-full items-center justify-center p-4 text-center'>
                        <TransitionChild
                            as={Fragment}
                            enter='ease-out duration-300'
                            enterFrom='opacity-0 scale-95'
                            enterTo='opacity-100 scale-100'
                            leave='ease-in duration-200'
                            leaveFrom='opacity-100 scale-100'
                            leaveTo='opacity-0 scale-95'
                        >
                            <DialogPanel className='relative w-full max-w-2xl mx-auto transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all'>
                                {title && (
                                    <DialogTitle
                                        as='h3'
                                        className='text-lg p-6 font-bold leading-6 text-gray-900 border-b'
                                    >
                                        {title}
                                    </DialogTitle>
                                )}

                                {/* Close button */}
                                <button
                                    onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        closeModal();
                                    }}
                                    aria-label='Close Modal'
                                    className='absolute top-4 right-4 z-10 p-2 rounded-full hover:bg-gray-100 transition-colors'
                                >
                                    <Image
                                        width={20}
                                        height={20}
                                        src='/x.png'
                                        alt='Close Button'
                                        className='w-6 h-6'
                                    />
                                </button>

                                {/* Modal content */}
                                <div className='relative'>
                                    {children}
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
};

export default Modal;
