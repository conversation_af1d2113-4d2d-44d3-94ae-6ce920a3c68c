import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { axiosInstance, patchForm } from '@/store/axiosInstance';
import { AxiosError } from 'axios';
import { OrderInterface, CreatorInterface } from '@/types/interfaces';

interface ErrorResponse {
  message: string;
  status?: number;
}

interface OrdersState {
  data: OrderInterface[];
  loading: boolean;
  error: string | null;
  currentOrder: OrderInterface | null;
}

const initialState: OrdersState = {
  data: [],
  loading: false,
  error: null,
  currentOrder: null,
};

// Assign Creators to Order
export const assignCreatorsToOrder = createAsyncThunk(
  'orders/assignCreators',
  async ({ orderId, assignedCreators }: { orderId: string, assignedCreators: string[] }, { rejectWithValue }) => {
    try {
      console.log("🚀 Assigning creators to order:", orderId);
      console.log("🚀 Creator IDs:", assignedCreators);

      const response = await axiosInstance.put(`/admin/orders/${orderId}/assignCreators`, {
        assignedCreators
      });

      console.log("✅ Assignment successful:", response.data);

      return {
        orderId,
        assignedCreatorIds: assignedCreators, // Store the IDs we sent
        assignedCreators: response.data.data?.assignedCreators || assignedCreators
      };
    } catch (error) {
      console.error("❌ Error assigning creators:", error);
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<any>;
        const errorMessage = axiosError.response?.data?.message ||
          axiosError.response?.data ||
          axiosError.message ||
          "Failed to assign creators";
        console.error("❌ Error details:", errorMessage);
        return rejectWithValue(errorMessage);
      }
      return rejectWithValue("Failed to assign creators");
    }
  }
);

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async ({ data }: { data: Partial<OrderInterface> }, { rejectWithValue }) => {
    try {


      const transformedData = {
        customer: typeof data.orderOwner === 'object' ? data.orderOwner._id : data.orderOwner,
        assignedCreators: Array.isArray(data.assignedCreators) ? data.assignedCreators : [data.assignedCreators],
        basePrice: parseFloat(data.basePrice?.toString() || '0'),
        noOfUgc: parseInt(data.noOfUgc?.toString() || '0', 10),
        additionalServices: {
          platform: data.additionalServices?.platform?.toLowerCase() || 'tiktok',
          duration: data.additionalServices?.duration || '15s',
          edit: data.additionalServices?.edit === true ? true : false,
          aspectRatio: data.additionalServices?.aspectRatio || '9:16',
          share: data.additionalServices?.share === true ? true : false,
          coverPicture: data.additionalServices?.coverPicture === true ? true : false,
          creatorType: data.additionalServices?.creatorType,
          productShipping: data.additionalServices?.productShipping === true ? true : false,
        },
        // Only include coupon if it exists and is not empty
        ...(data.coupon && data.coupon.trim() !== '' && { coupon: data.coupon }),
      };

      // Debug logging to see what's being sent
      console.log('🚀 Original data.coupon:', data.coupon);
      console.log('🚀 TransformedData being sent to backend:', transformedData);

      // First create the order
      const response = await axiosInstance.post('/admin/orders', transformedData);

      // Then fetch the complete order details to get the full owner information
      const fullOrderResponse = await axiosInstance.get(`/admin/orders/${response.data.data._id}`);

      return fullOrderResponse.data.data;
    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to create order');
      }
      return rejectWithValue('Failed to create order');
    }
  }
);

export const approvePayment = createAsyncThunk(
  'orders/approvePayment',
  async ({ orderId }: { orderId: string; }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.patch(`/admin/orders/approve-payment/${orderId}`);

      return response.data.data;
    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to approve payment');
      }
      return rejectWithValue('Failed to approve payment');
    }
  }
);

// Fetch All Orders
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get('/admin/orders');

      // Sort orders by createdAt before returning
      const sortedOrders = response.data.data.sort((a: OrderInterface, b: OrderInterface) => {
        // Add type safety for createdAt field
        const dateA = new Date(a.createdAt || Date.now()).getTime();
        const dateB = new Date(b.createdAt || Date.now()).getTime();
        return dateB - dateA; // Newest first
      });

      return sortedOrders;

    } catch (error) {

      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to fetch orders');
      }

      return rejectWithValue('Failed to fetch orders');
    }
  }
);

// Fetch Single Order
export const fetchOrderById = createAsyncThunk(
  'orders/fetchOrderById',
  async ({ orderId }: { orderId: string; }, { rejectWithValue }) => {

    try {
      const response = await axiosInstance.get(`/admin/orders/${orderId}`);

      return response.data.data;
    } catch (error) {

      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to fetch order');
      }

      return rejectWithValue('Failed to fetch order');
    }
  }
);

// Update Order
export const updateOrder = createAsyncThunk(
  'orders/updateOrder',
  async ({ orderId, data }: { orderId: string; data: FormData; }, { rejectWithValue }) => {


    try {
      const response = await patchForm(`/admin/orders/${orderId}`, data);

      return response.data.data;
    } catch (error) {

      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to update order');
      }

      return rejectWithValue('Failed to update order');
    }
  }
);

// Delete Order
export const deleteOrder = createAsyncThunk(
  'orders/deleteOrder',
  async ({ orderId }: { orderId: string; }, { rejectWithValue }) => {

    try {
      const response = await axiosInstance.delete(`/admin/orders/${orderId}`);

      return { orderId, data: response.data.data };
    } catch (error) {

      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to delete order');
      }

      return rejectWithValue('Failed to delete order');
    }
  }
);

// Approve Creator
export const approveCreator = createAsyncThunk(
  'orders/approveCreator',
  async ({ orderId, creatorId }: { orderId: string; creatorId: string; }, { rejectWithValue }) => {

    try {
      const response = await axiosInstance.patch(
        `/admin/orders/approve-creator/${orderId}/${creatorId}`,
        { creatorId },

      );
      return response.data.data;
    } catch (error) {

      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to approve creator');
      }

      return rejectWithValue('Failed to approve creator');
    }
  }
);

// Reject Creator
export const rejectCreator = createAsyncThunk(
  'orders/rejectCreator',
  async ({ orderId, creatorId }: { orderId: string; creatorId: string; }, { rejectWithValue }) => {

    try {
      const response = await axiosInstance.patch(
        `/admin/orders/reject-creator/${orderId}/${creatorId}`,
        { creatorId },

      );

      return response.data.data;
    } catch (error) {

      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to reject creator');
      }

      return rejectWithValue('Failed to reject creator');
    }
  }
);

export const updatePaymentNote = createAsyncThunk(
  "orders/updatePaymentNote",
  async ({ orderId, paymentNote }: { orderId: string, paymentNote: string }, { rejectWithValue }) => {
    try {
      console.log("🚀 API Call - Updating payment note:", { orderId, paymentNote });

      const response = await axiosInstance.patch(`/admin/orders/${orderId}/updatePaymentNote`, {
        paymentNote
      });

      console.log("✅ API Response:", response.data);

      return {
        orderId,
        paymentNote: response.data.data?.paymentNote || paymentNote
      };
    } catch (error) {
      console.error("❌ API Error:", error);

      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<any>;
        const errorMessage = axiosError.response?.data?.message ||
          axiosError.response?.data ||
          "Failed to save the payment note";
        return rejectWithValue(errorMessage);
      }

      return rejectWithValue('Failed to update payment note');
    }
  }
)

// Get Applied Creators
export const getAppliedCreators = createAsyncThunk(
  'orders/getAppliedCreators',
  async ({ orderId }: { orderId: string; }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/admin/orders/applied-creators/${orderId}`);

      return {
        orderId,
        creators: response.data.data.appliedCreators // This now contains the full Creator objects
      };

    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to fetch applied creators');
      }

      return rejectWithValue('Failed to fetch applied creators');
    }
  }
);


export const getAssignedOrders = createAsyncThunk(
  'orders/getAssignedCreators',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/admin/orders/assigned-orders`);

      return response.data.data

    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to fetch applied creators');
      }

      return rejectWithValue('Failed to fetch applied creators');
    }
  }
);

// Mark entire order as completed (for Orders.tsx)
export const markTheOrderAsCompleted = createAsyncThunk(
  'orders/markTheOrderAsCompleted',
  async ({ orderId }: { orderId: string; }, { rejectWithValue }) => {
    console.log("Order ID in Request :", orderId);

    try {
      const response = await axiosInstance.patch(
        `/admin/orders/mark-as-completed/${orderId}`
      );
      return response.data.data
    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to mark the order as completed');
      }

      return rejectWithValue('Failed to mark the order as completed');
    }
  }
);

// Mark specific file as completed (for ViewOrderModal.tsx)
export const markFileAsCompleted = createAsyncThunk(
  'orders/markFileAsCompleted',
  async ({ orderId, fileId }: { orderId: string; fileId: string; }, { rejectWithValue }) => {
    console.log("File ID in Request :", fileId);
    console.log("Order ID in Request :", orderId);

    try {
      // Try with fileId as URL parameter instead of body
      const response = await axiosInstance.patch(
        `/admin/orders/mark-file-as-completed/${orderId}/${fileId}`
      );
      return response.data.data
    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to mark the file as completed');
      }

      return rejectWithValue('Failed to mark the order as completed');
    }
  }
)

export const updatePaymentStatus = createAsyncThunk(
  'orders/updatePaymentStatus',
  async ({ orderid, paymentstatus }: { orderid: string; paymentstatus: string; }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.put('/admin/orders/updatepaymentstatus', {
        orderid,
        paymentstatus
      });
      return response.data.data;
    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to update payment status');
      }
      return rejectWithValue('Failed to update payment status');
    }
  }
);

// Mark entire order as rejected (for Orders.tsx)
export const markTheOrderAsRejected = createAsyncThunk(
  'orders/markTheOrderAsRejected',
  async ({ orderId }: { orderId: string; }, { rejectWithValue }) => {
    console.log("Order ID in Request :", orderId);

    try {
      const response = await axiosInstance.patch(
        `/admin/orders/mark-as-rejected/${orderId}`
      );
      return response.data.data
    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to mark the order as rejected');
      }

      return rejectWithValue('Failed to mark the order as rejected');
    }
  }
);

// Mark specific file as rejected (for ViewOrderModal.tsx)
export const markFileAsRejected = createAsyncThunk(
  'orders/markFileAsRejected',
  async ({ orderId, fileId }: { orderId: string; fileId: string; }, { rejectWithValue }) => {
    console.log("File ID in Request :", fileId);
    console.log("Order ID in Request :", orderId);

    try {
      // Try with fileId as URL parameter instead of body
      const response = await axiosInstance.patch(
        `/admin/orders/mark-file-as-rejected/${orderId}/${fileId}`
      );
      return response.data.data
    } catch (error) {
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError<ErrorResponse>;
        return rejectWithValue(axiosError.response?.data?.message || 'Failed to mark the file as rejected');
      }

      return rejectWithValue('Failed to mark the order as rejected');
    }
  }
)

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setCurrentOrder: (state, action: PayloadAction<OrderInterface>) => {
      state.currentOrder = action.payload;
    },
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
    },
    clearOrdersError: (state) => {
      state.error = null;
    },
    updateOrderPaymentStatusLocally: (state, action: PayloadAction<{ orderId: string; paymentStatus: OrderInterface['paymentStatus'] }>) => {
      const { orderId, paymentStatus } = action.payload;
      const index = state.data.findIndex(order => order._id === orderId);
      if (index !== -1) {
        state.data[index].paymentStatus = paymentStatus;
        if (state.currentOrder?._id === orderId) {
          state.currentOrder.paymentStatus = paymentStatus;
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Assign Creators to Order
      .addCase(assignCreatorsToOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(assignCreatorsToOrder.fulfilled, (state, action) => {
        state.loading = false;
        console.log("🔄 Updating Redux state after assignment:", action.payload);

        const { orderId, assignedCreatorIds } = action.payload;

        // Update the current order's assignedCreators with the new assignments
        if (state.currentOrder?._id === orderId) {
          console.log("🔄 Updating current order assigned creators");
          console.log("🔄 Old assigned creators:", state.currentOrder.assignedCreators);
          console.log("🔄 New creator IDs:", assignedCreatorIds);

          // Filter the existing assignedCreators to only include the ones that are still assigned
          if (Array.isArray(state.currentOrder.assignedCreators)) {
            state.currentOrder.assignedCreators = state.currentOrder.assignedCreators.filter(
              (creator: any) => {
                const creatorId = typeof creator === 'string' ? creator : creator._id;
                return assignedCreatorIds.includes(creatorId);
              }
            );
          }

          console.log("🔄 Updated assigned creators:", state.currentOrder.assignedCreators);
        }

        // Update the order in the data array as well
        const orderIndex = state.data.findIndex(order => order._id === orderId);
        if (orderIndex !== -1) {
          console.log("🔄 Updating order in data array");
          if (Array.isArray(state.data[orderIndex].assignedCreators)) {
            state.data[orderIndex].assignedCreators = state.data[orderIndex].assignedCreators.filter(
              (creator: any) => {
                const creatorId = typeof creator === 'string' ? creator : creator._id;
                return assignedCreatorIds.includes(creatorId);
              }
            );
          }
        }

        console.log("✅ Redux state updated successfully");
      })
      .addCase(assignCreatorsToOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update Payment Note
      .addCase(updatePaymentNote.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePaymentNote.fulfilled, (state, action) => {
        state.loading = false;
        console.log("🔄 Updating payment note in Redux state:", action.payload);

        const { orderId, paymentNote } = action.payload;

        // Update the current order's payment note
        if (state.currentOrder?._id === orderId) {
          state.currentOrder.paymentNote = paymentNote;
        }

        // Update the order in the data array
        const orderIndex = state.data.findIndex(order => order._id === orderId);
        if (orderIndex !== -1) {
          state.data[orderIndex].paymentNote = paymentNote;
        }

        console.log("✅ Payment note updated successfully in Redux state");
      })
      .addCase(updatePaymentNote.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Add these cases to your extraReducers:
      .addCase(getAppliedCreators.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAppliedCreators.fulfilled, (state, action) => {
        state.loading = false;
        // Update the current order's appliedCreators if it matches the orderId
        if (state.currentOrder?._id === action.payload.orderId) {
          state.currentOrder.appliedCreators = action.payload.creators;
        }
        // Update the current order's appliedCreators with the full Creator objects
        if (state.currentOrder?._id === action.payload.orderId) {
          state.currentOrder.appliedCreators = action.payload.creators;
        }
        // Update the order in the data array
        const index = state.data.findIndex(order => order._id === action.payload.orderId);
        if (index !== -1) {
          state.data[index].appliedCreators = action.payload.creators;
        }
      })
      .addCase(getAppliedCreators.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getAssignedOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAssignedOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(getAssignedOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Create Order
      .addCase(createOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createOrder.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        // Add new order at the beginning since it's the newest
        state.data.unshift(action.payload);
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Orders
      .addCase(fetchOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action: PayloadAction<OrderInterface[]>) => {
        state.loading = false;
        state.data = action.payload; // Data is already sorted
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Single Order
      .addCase(fetchOrderById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrderById.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        state.currentOrder = action.payload;
      })
      .addCase(fetchOrderById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update Order
      .addCase(updateOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateOrder.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          state.data[index] = action.payload;
          if (state.currentOrder?._id === action.payload._id) {
            state.currentOrder = action.payload;
          }
        }
      })
      .addCase(updateOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete Order
      .addCase(deleteOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.data = state.data.filter(order => order._id !== action.payload.orderId);
        if (state.currentOrder?._id === action.payload.orderId) {
          state.currentOrder = null;
        }
      })
      .addCase(deleteOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Approve Creator
      .addCase(approveCreator.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(approveCreator.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          state.data[index] = action.payload;
          if (state.currentOrder?._id === action.payload._id) {
            state.currentOrder = action.payload;
          }
        }
      })
      .addCase(approveCreator.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Reject Creator
      .addCase(rejectCreator.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(rejectCreator.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          state.data[index] = action.payload;
        }
      })
      .addCase(rejectCreator.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(markTheOrderAsCompleted.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(markTheOrderAsCompleted.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          // Merge the response with existing data to preserve all fields
          state.data[index] = { ...state.data[index], ...action.payload };
          if (state.currentOrder?._id === action.payload._id) {
            // Merge with current order to preserve all fields
            state.currentOrder = { ...state.currentOrder, ...action.payload };
          }
        }
      })
      .addCase(markTheOrderAsCompleted.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      .addCase(markTheOrderAsRejected.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(markTheOrderAsRejected.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          // Merge the response with existing data to preserve all fields
          state.data[index] = { ...state.data[index], ...action.payload };
          if (state.currentOrder?._id === action.payload._id) {
            // Merge with current order to preserve all fields
            state.currentOrder = { ...state.currentOrder, ...action.payload };
          }
        }
      })
      .addCase(markTheOrderAsRejected.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Mark File as Completed
      .addCase(markFileAsCompleted.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(markFileAsCompleted.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          // Preserve important populated fields that might be lost in API response
          const preservedOrderOwner = state.data[index].orderOwner;
          const preservedAssignedCreators = state.data[index].assignedCreators;

          // Merge the response with existing data
          state.data[index] = {
            ...state.data[index],
            ...action.payload,
            // Preserve populated fields if API response has them as IDs only
            orderOwner: typeof action.payload.orderOwner === 'string' ? preservedOrderOwner : action.payload.orderOwner,
            assignedCreators: Array.isArray(action.payload.assignedCreators) && action.payload.assignedCreators.length > 0 && typeof action.payload.assignedCreators[0] === 'string'
              ? preservedAssignedCreators
              : action.payload.assignedCreators
          };

          if (state.currentOrder?._id === action.payload._id) {
            // Same logic for current order
            const preservedCurrentOrderOwner = state.currentOrder.orderOwner;
            const preservedCurrentAssignedCreators = state.currentOrder.assignedCreators;

            state.currentOrder = {
              ...state.currentOrder,
              ...action.payload,
              orderOwner: typeof action.payload.orderOwner === 'string' ? preservedCurrentOrderOwner : action.payload.orderOwner,
              assignedCreators: Array.isArray(action.payload.assignedCreators) && action.payload.assignedCreators.length > 0 && typeof action.payload.assignedCreators[0] === 'string'
                ? preservedCurrentAssignedCreators
                : action.payload.assignedCreators
            };
          }
        }
      })
      .addCase(markFileAsCompleted.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Mark File as Rejected
      .addCase(markFileAsRejected.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(markFileAsRejected.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          // Preserve important populated fields that might be lost in API response
          const preservedOrderOwner = state.data[index].orderOwner;
          const preservedAssignedCreators = state.data[index].assignedCreators;

          // Merge the response with existing data
          state.data[index] = {
            ...state.data[index],
            ...action.payload,
            // Preserve populated fields if API response has them as IDs only
            orderOwner: typeof action.payload.orderOwner === 'string' ? preservedOrderOwner : action.payload.orderOwner,
            assignedCreators: Array.isArray(action.payload.assignedCreators) && action.payload.assignedCreators.length > 0 && typeof action.payload.assignedCreators[0] === 'string'
              ? preservedAssignedCreators
              : action.payload.assignedCreators
          };

          if (state.currentOrder?._id === action.payload._id) {
            // Same logic for current order
            const preservedCurrentOrderOwner = state.currentOrder.orderOwner;
            const preservedCurrentAssignedCreators = state.currentOrder.assignedCreators;

            state.currentOrder = {
              ...state.currentOrder,
              ...action.payload,
              orderOwner: typeof action.payload.orderOwner === 'string' ? preservedCurrentOrderOwner : action.payload.orderOwner,
              assignedCreators: Array.isArray(action.payload.assignedCreators) && action.payload.assignedCreators.length > 0 && typeof action.payload.assignedCreators[0] === 'string'
                ? preservedCurrentAssignedCreators
                : action.payload.assignedCreators
            };
          }
        }
      })
      .addCase(markFileAsRejected.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update Payment Status
      .addCase(updatePaymentStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePaymentStatus.fulfilled, (state, action: PayloadAction<OrderInterface>) => {
        state.loading = false;
        const index = state.data.findIndex(order => order._id === action.payload._id);
        if (index !== -1) {
          state.data[index] = action.payload;
          if (state.currentOrder?._id === action.payload._id) {
            state.currentOrder = action.payload;
          }
        }
      })
      .addCase(updatePaymentStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

  },
});

export const { setCurrentOrder, clearCurrentOrder, clearOrdersError, updateOrderPaymentStatusLocally } = ordersSlice.actions;

export default ordersSlice.reducer;