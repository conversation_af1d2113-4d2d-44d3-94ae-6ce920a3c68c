'use client';

import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from "next/navigation";
import { Lock, Loader2 } from 'lucide-react';
import { toast } from 'react-toastify';

import { useFileContext } from '@/context/FileContext';
import { createOrder, createPaymentToken } from '@/store/features/profile/orderSlice';
import { AppDispatch } from '@/store/store';

interface PayTRTokenData {
  merchant_id: string;
  user_ip: string;
  merchant_oid: string;
  email: string;
  payment_amount: string;
  payment_type: string;
  installment_count: string;
  currency: string;
  test_mode: string;
  non_3d: string;
  merchant_ok_url: string;
  merchant_fail_url: string;
  user_name: string;
  user_address: string;
  user_phone: string;
  user_basket: string;
  paytr_token: string;
  debug_on: string;
  client_lang: string;
}

interface LocalUser {
  name: string;
  email: string;
  phone: string;
  address: string;
  amount?: string;
}

export default function PayTRForm() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const [tokenData, setTokenData] = useState<PayTRTokenData | null>(null);
  const [user, setUser] = useState<LocalUser | null>(null);
  const { selectedFiles, setSelectedFiles } = useFileContext();
  const orderId = useSelector((state: any) => state.order?.orderFormData?.paymentInfo?.orderId);
  const isLoading = useSelector((state: any) => state.order?.loading);
  const [buttonloading , setbuttonloading] = useState(false)


  
  const hasTriggered = useRef(false); // ✅ prevents multiple calls

  useEffect(() => {
    const fetchPayTRToken = async () => {
      try {
        const storedUser = localStorage.getItem('user');
        const storedUserData = localStorage.getItem('userdata');

        if (!storedUser || !storedUserData) return;

        const userObj = JSON.parse(storedUser);
        const userData = JSON.parse(storedUserData);

        const localUser: LocalUser = {
          name: userData.name,
          email: userData.email || '',
          phone: userData.phoneNumber || '',
          address: userData.address || '',
          amount: userData.amount || '',
        };

        setUser(localUser);

        const paymentData = {
          amount: userData.amount,
          orderId: orderId,
          userEmail: userData.email,
          userName: userObj.name || 'Ad Soyad',
          userPhone: userData.phoneNumber,
          installment_count: 0,
        };

        // 1️⃣ Call payment API using Redux thunk
        const tokenResult = await dispatch(createPaymentToken({ paymentData })).unwrap();
        setTokenData(tokenResult);

        // 2️⃣ Call createOrder AFTER token is generated
        await dispatch(createOrder({ selectedFiles })).unwrap();
        setbuttonloading(true)
        setSelectedFiles([]);
        toast.success("Sipariş başarıyla oluşturuldu!");
       
        setSelectedFiles([]);
        setbuttonloading(false);
        toast.success("Sipariş başarıyla oluşturuldu!");
      } catch (error: any) {
        console.error('Token fetch or order creation error:', error.message || error);
        toast.error(error.message || "Sipariş oluşturulurken bir hata oluştu.");
        setSelectedFiles([]);
      }
    };

    if (orderId && !hasTriggered.current) {
      hasTriggered.current = true; // ✅ lock before call
      fetchPayTRToken();
    }
  }, [orderId, dispatch, selectedFiles, setSelectedFiles]);

  if (!tokenData) {
    return <p className="text-center mt-10 text-gray-600">Yükleniyor...</p>;
  }

  return (
    <div className="max-w-md mx-auto mt-10 bg-white p-8 rounded-2xl shadow-lg relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-2xl z-10">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-blue-600" />
            <p className="text-gray-600 font-medium">
              {!tokenData ? "Ödeme işlemi hazırlanıyor..." : "Sipariş oluşturuluyor..."}
            </p>
          </div>
        </div>
      )}
      <h2 className="text-2xl font-bold text-center text-gray-800 mb-6">Kart ile Güvenli Ödeme</h2>

      <form
        action="https://www.paytr.com/odeme"
        method="POST"
        className="space-y-4"
        onSubmit={(e) => {
          if (isLoading) {
            e.preventDefault();
            toast.warning("Lütfen işlem tamamlanana kadar bekleyin...");
          }
        }}
      >
        {Object.entries(tokenData).map(([key, value]) => (
          <input key={key} type="hidden" name={key} value={value} />
        ))}

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Kart Sahibinin Adı</label>
          <input
            name="cc_owner"
            placeholder="Örnek: Ad Soyad"
            required
            disabled={isLoading}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              isLoading ? 'bg-gray-100 cursor-not-allowed' : ''
            }`}
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Kart Numarası</label>
          <input
            name="card_number"
            placeholder="XXXX XXXX XXXX XXXX"
            required
            disabled={isLoading}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              isLoading ? 'bg-gray-100 cursor-not-allowed' : ''
            }`}
          />
        </div>

        <div className="flex space-x-4">
          <div className="w-1/2">
            <label className="block text-sm font-medium text-gray-700">Ay (MM)</label>
            <input
              name="expiry_month"
              placeholder="MM"
              required
              disabled={isLoading}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                isLoading ? 'bg-gray-100 cursor-not-allowed' : ''
              }`}
            />
          </div>
          <div className="w-1/2">
            <label className="block text-sm font-medium text-gray-700">Yıl (YY)</label>
            <input
              name="expiry_year"
              placeholder="YY"
              required
              disabled={isLoading}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                isLoading ? 'bg-gray-100 cursor-not-allowed' : ''
              }`}
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">CVV</label>
          <input
            name="cvv"
            placeholder="CVV"
            required
            disabled={isLoading}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              isLoading ? 'bg-gray-100 cursor-not-allowed' : ''
            }`}
          />
        </div>

        {user && (
          <div className="pt-4 space-y-3 text-sm text-gray-700">
            <div>
              <label className="block font-semibold">Ad Soyad</label>
              <input
                type="text"
                value={user.name}
                disabled
                className="w-full bg-gray-100 border rounded px-3 py-2 cursor-not-allowed"
              />
            </div>
            <div>
              <label className="block font-semibold">Email</label>
              <input
                type="email"
                value={user.email}
                disabled
                className="w-full bg-gray-100 border rounded px-3 py-2 cursor-not-allowed"
              />
            </div>
            <div>
              <label className="block font-semibold">Telefon</label>
              <input
                type="text"
                value={user.phone}
                disabled
                className="w-full bg-gray-100 border rounded px-3 py-2 cursor-not-allowed"
              />
            </div>
          </div>
        )}

        <div className="pt-6">
          <button

            type="submit"
            disabled={isLoading || buttonloading}
            className={`w-full font-semibold py-3 rounded-md transition flex items-center justify-center gap-2 ${
              isLoading
                ? 'bg-gray-400 cursor-not-allowed text-gray-200'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                İşleniyor...
              </>
            ) : (
              <>
                <Lock className="w-5 h-5" />
                Ödemeyi Tamamla
              </>
            )}
          </button>
        </div>
      </form>

      <div className="mt-6 flex items-center text-sm text-gray-500 justify-center">
        <Lock className="w-4 h-4 mr-1" />
        <span>Ödemeniz Contentia.io ve PayTR tarafından güvence altındadır</span>
      </div>
    </div>
  );
}
