import { fetchAdditionalServices } from "@/store/features/admin/addPriceSlice";
import { RootState } from "@/store/store";
import { CreatorInterface, OrderInterface } from "@/types/interfaces";
import { checkStatus } from "@/utils/CheckOrderStatus";
import React, { useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FaCheck, FaTimes } from "react-icons/fa";
import { markFileAsCompleted, markFileAsRejected } from "@/store/features/admin/ordersSlice";
import { toast } from "react-toastify";
import { AppDispatch } from "@/store/store";

interface ViewModalProps {
    order: OrderInterface | null;
}

const ViewModal = ({ order }: ViewModalProps) => {
    if (!order) return null;

    // Debug: Log the order data to see what's happening
    console.log("ViewModal order data:", order);
    console.log("Order uploadFiles:", order.uploadFiles);
    console.log("Order owner:", order.orderOwner);
    console.log("Order owner fullName:", order.orderOwner?.fullName);
    console.log("Order owner email:", order.orderOwner?.email);
    console.log("Order owner keys:", order.orderOwner ? Object.keys(order.orderOwner) : 'No orderOwner');

    const { data: additionalService } = useSelector(
        (state: RootState) => state.addPrice
    );
    const dispatch = useDispatch<AppDispatch>();
    const quantity = order.noOfUgc;
    const basePrice = order.basePrice;

    useEffect(() => {
        dispatch(fetchAdditionalServices() as any);
    }, [dispatch]);

    const onMarkAsRejected = useCallback(
        async (orderId: string, fileId: string) => {
            try {
                const result = await dispatch(
                    markFileAsRejected({ orderId, fileId })
                ).unwrap();
                console.log("Mark as rejected API response:", result);
                toast.success("File marked as rejected successfully!");
            } catch (error) {
                console.error("Error marking file as rejected:", error);
                toast.error("Error marking the file as rejected.");
            }
        },
        [dispatch]
    );

    const onMarkAsCompleted = useCallback(
        async (orderId: string, fileId: string) => {
            try {
                const result = await dispatch(
                    markFileAsCompleted({ orderId, fileId })
                ).unwrap();
                console.log("Mark as completed API response:", result);
                toast.success("File marked as completed successfully!");
            } catch (error) {
                console.error("Error marking file as completed:", error);
                toast.error("Error marking the file as completed.");
            }
        },
        [dispatch]
    );

    return (
        <>
            <div className='flex justify-center items-center'>
                <div className='bg-white rounded-lg p-6 w-full max-w-6xl'>
                    <h2 className='text-lg font-bold mb-4 BlueText'>
                        Order Details
                    </h2>

                    {/* Two-column Layout */}
                    <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 text-sm'>
                        {/* Left Column - Basic & Additional Services */}
                        <div className='space-y-6'>
                            {/* Order Basic Details */}
                            <h3 className='text-lg font-bold mb-3 BlueText'>
                                Basic Details
                            </h3>
                            <div className='grid grid-cols-2 gap-4 mb-4'>
                                <div className='text-gray-700 font-semibold'>
                                    Order ID:
                                </div>
                                <div className='text-right font-bold BlueText break-all'>
                                    {order.orderId || order._id}
                                </div>

                                <div className='text-gray-700 font-semibold'>
                                    Order Owner:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {typeof order.orderOwner === 'object' && order.orderOwner?.fullName
                                        ? order.orderOwner.fullName
                                        : order.customerInfo?.companyName || 'No Title'}
                                </div>

                                <div className='text-gray-700 font-semibold'>
                                    Assigned Creators:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {order.assignedCreators.length}
                                </div>

                                <div className='text-gray-700 font-semibold'>
                                    Order Status:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {checkStatus(order.orderStatus)}
                                </div>

                                <div className='text-gray-700 font-semibold'>
                                    Payment Status:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {order.paymentStatus}
                                </div>
                            </div>

                            {/* Order Summary */}
                            <div className='mt-6'>
                                <div className='bg-white rounded-md w-full'>
                                    <h2 className='BlueText text-lg font-semibold mb-4'>
                                        Markalarım Özeti:
                                    </h2>
                                    <div className='flex items-center justify-between'>
                                        <div>
                                            <p className='font-semibold'>
                                                {quantity} Videos ({order.additionalServices?.platform || "Platform belirtilmemiş"})
                                            </p>
                                            <p className='text-sm text-gray-500'>
                                                {basePrice
                                                    ? (
                                                        basePrice / quantity
                                                    ).toLocaleString("tr-TR")
                                                    : "0"}{" "}
                                                TL / Video
                                            </p>
                                        </div>
                                        <p className='font-semibold BlueText'>
                                            {basePrice?.toLocaleString(
                                                "tr-TR"
                                            ) ?? "0"}{" "}
                                            TL
                                        </p>
                                    </div>
                                    {/* Additional Services */}
                                    {order?.additionalServices?.duration &&
                                        ["30s", "60s"].includes(
                                            order.additionalServices.duration
                                        ) && (
                                            <div className='flex items-center justify-between'>
                                                <div>
                                                    <p className='font-semibold'>
                                                        Süre
                                                    </p>
                                                    <p className='text-sm'>
                                                        {order
                                                            .additionalServices
                                                            .duration === "30s"
                                                            ? `${(
                                                                additionalService?.thirtySecondDurationPrice ??
                                                                0
                                                            ).toLocaleString(
                                                                "tr-TR"
                                                            )} TL / Video`
                                                            : order
                                                                .additionalServices
                                                                .duration ===
                                                                "60s"
                                                                ? `${(
                                                                    additionalService?.sixtySecondDurationPrice ??
                                                                    0
                                                                ).toLocaleString(
                                                                    "tr-TR"
                                                                )} TL / Video`
                                                                : ""}
                                                    </p>
                                                </div>
                                                <p className='font-semibold BlueText'>
                                                    {order.additionalServices
                                                        .duration === "30s"
                                                        ? `${(
                                                            (additionalService?.thirtySecondDurationPrice ??
                                                                0) * quantity
                                                        ).toLocaleString(
                                                            "tr-TR"
                                                        )} TL`
                                                        : order
                                                            .additionalServices
                                                            .duration ===
                                                            "60s"
                                                            ? `${(
                                                                (additionalService?.sixtySecondDurationPrice ??
                                                                    0) * quantity
                                                            ).toLocaleString(
                                                                "tr-TR"
                                                            )} TL`
                                                            : ""}
                                                </p>
                                            </div>
                                        )}

                                    {order?.additionalServices?.edit && (
                                        <div className='flex items-center justify-between'>
                                            <div>
                                                <p className='font-semibold'>
                                                    Düzenleme
                                                </p>
                                                <p className='text-sm text-gray-500'>
                                                    {(
                                                        additionalService?.editPrice ??
                                                        0
                                                    ).toLocaleString(
                                                        "tr-TR"
                                                    )}{" "}
                                                    TL / Video
                                                </p>
                                            </div>
                                            <p className='font-semibold BlueText'>
                                                {(
                                                    (additionalService?.editPrice ??
                                                        0) * quantity
                                                ).toLocaleString("tr-TR")}{" "}
                                                TL
                                            </p>
                                        </div>
                                    )}

                                    {order?.additionalServices?.share && (
                                        <div className='flex items-center justify-between'>
                                            <div>
                                                <p className='font-semibold'>
                                                    Sosyal Medyada Paylaşım
                                                </p>
                                                <p className='text-sm text-gray-500'>
                                                    {(
                                                        additionalService?.sharePrice ??
                                                        0
                                                    ).toLocaleString(
                                                        "tr-TR"
                                                    )}{" "}
                                                    TL / Video
                                                </p>
                                            </div>
                                            <p className='font-semibold BlueText'>
                                                {(
                                                    (additionalService?.sharePrice ??
                                                        0) * quantity
                                                ).toLocaleString("tr-TR")}{" "}
                                                TL
                                            </p>
                                        </div>
                                    )}

                                    {order?.additionalServices?.coverPicture && (
                                        <div className='flex items-center justify-between'>
                                            <div>
                                                <p className='font-semibold'>
                                                    Kapak Görseli
                                                </p>
                                                <p className='text-sm text-gray-500'>
                                                    {(
                                                        additionalService?.coverPicPrice ??
                                                        0
                                                    ).toLocaleString(
                                                        "tr-TR"
                                                    )}{" "}
                                                    TL / Video
                                                </p>
                                            </div>
                                            <p className='font-semibold BlueText'>
                                                {(
                                                    (additionalService?.coverPicPrice ??
                                                        0) * quantity
                                                ).toLocaleString("tr-TR")}{" "}
                                                TL
                                            </p>
                                        </div>
                                    )}

                                    {order?.additionalServices?.creatorType && (
                                        <div className='flex items-center justify-between'>
                                            <div>
                                                <p className='font-semibold'>
                                                    Influencer Paketi
                                                </p>
                                                <p className='text-sm text-gray-500'>
                                                    {(
                                                        additionalService?.creatorTypePrice ??
                                                        0
                                                    ).toLocaleString(
                                                        "tr-TR"
                                                    )}{" "}
                                                    TL / Video
                                                </p>
                                            </div>
                                            <p className='font-semibold BlueText'>
                                                {(
                                                    (additionalService?.creatorTypePrice ??
                                                        0) * quantity
                                                ).toLocaleString("tr-TR")}{" "}
                                                TL
                                            </p>
                                        </div>
                                    )}

                                    {order?.additionalServices?.productShipping && (
                                        <div className='flex items-center justify-between'>
                                            <div>
                                                <p className='font-semibold'>
                                                    Ürün Gönderimi
                                                </p>
                                                <p className='text-sm text-gray-500'>
                                                    {(
                                                        additionalService?.shippingPrice ??
                                                        0
                                                    ).toLocaleString(
                                                        "tr-TR"
                                                    )}{" "}
                                                    TL / Video
                                                </p>
                                            </div>
                                            <p className='font-semibold BlueText'>
                                                {(
                                                    (additionalService?.shippingPrice ??
                                                        0) * quantity
                                                ).toLocaleString("tr-TR")}{" "}
                                                TL
                                            </p>
                                        </div>
                                    )}

                                    <hr className='my-4 border-gray-200' />
                                    <div className='flex items-center justify-between'>
                                        <div className='text-gray-700 font-semibold text-lg'>
                                            Total Price:
                                        </div>
                                        <div className='text-right font-bold BlueText text-lg'>
                                            {order?.totalPriceForCustomer?.toLocaleString(
                                                "tr-TR"
                                            )}{" "}
                                            TL
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Additional Services Summary */}
                            <h3 className='text-lg font-bold mt-6 mb-3 BlueText'>
                                Additional Services
                            </h3>
                            <div className='grid grid-cols-2 gap-4'>
                                <div className='text-gray-700'>Süre:</div>
                                <div className='text-right font-bold BlueText'>
                                    {order.additionalServices?.duration ||
                                        "15s (Standart)"}
                                </div>

                                <div className='text-gray-700'>
                                    En Boy Oranı:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {order.additionalServices?.aspectRatio ||
                                        "Not specified"}
                                </div>

                                <div className='text-gray-700'>
                                    Revision Note:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {(order.revisions ?? [])[0]?.revisionContent || "Yok"}
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Brief Content & Preferences */}
                        <div className='space-y-6'>
                            {/* Brief Content Details */}
                            <h3 className='text-lg font-bold mb-3 BlueText'>
                                Brief Content
                            </h3>
                            <div className='grid grid-cols-2 gap-4'>
                                <div className='text-gray-700'>Brand Name:</div>
                                <div className='text-right font-bold BlueText'>
                                    {order.briefContent?.brandName ||
                                        "Not specified"}
                                </div>
                                <div>
                                    <div className='text-gray-700'>Brief:</div>
                                    <div className='w-[350px]'>
                                        <p className='w-full font-bold BlueText'>
                                            {order.briefContent?.brief ||
                                                "Not specified"}
                                        </p>
                                    </div>
                                </div>
                                <div></div>
                                <div className='text-gray-700'>
                                    Product/Service Name:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {order.briefContent?.productServiceName ||
                                        "Not specified"}
                                </div>
                                <div>
                                    <div className='text-gray-700'>
                                        Product/Service Description:
                                    </div>
                                    <div className='w-[350px]'>
                                        <p className='w-full font-bold BlueText'>
                                            {order.briefContent
                                                ?.productServiceDesc ||
                                                "Not specified"}
                                        </p>
                                    </div>
                                </div>
                                <div></div>

                                <div>
                                    <div className='text-gray-700'>
                                        Scenario:
                                    </div>
                                    <div className='w-[350px]'>
                                        <p className='w-full font-bold BlueText'>
                                            {order.briefContent?.scenario ||
                                                "Not specified"}
                                        </p>
                                    </div>
                                </div>
                                <div></div>

                                <div className='text-gray-700'>Case Study:</div>
                                <div className='w-[350px]'>
                                    <p className='w-full font-bold BlueText'>
                                        {order.briefContent?.caseStudy ||
                                            "Not specified"}
                                    </p>
                                </div>
                            </div>

                            {/* Preferences */}
                            <h3 className='text-lg font-bold mt-6 mb-3 BlueText'>
                                Preferences
                            </h3>
                            <div className='grid grid-cols-2 gap-4'>
                                <div className='text-gray-700'>
                                    Content Type:
                                </div>
                                <div className='text-right font-bold BlueText'>
                                    {order.preferences?.contentType ||
                                        "Not specified"}
                                </div>

                                <div className='text-gray-700'>Gender:</div>
                                <div className='text-right font-bold BlueText'>
                                    {order.preferences?.creatorGender ||
                                        "Not specified"}
                                </div>

                                <div className='text-gray-700'>Age Range:</div>
                                <div className='text-right font-bold BlueText'>
                                    {order.preferences?.minCreatorAge} -{" "}
                                    {order.preferences?.maxCreatorAge} years
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Table */}
            <div className='bg-white my-8 px-4 sm:px-6 md:px-12 overflow-x-auto'>
                <h2 className='text-lg font-bold mb-4 BlueText'>
                    Order Files Information
                </h2>
                {order?.assignedCreators?.length > 0 ? (
                    <table className='text-xs lg:text-sm w-full bg-white table-auto'>
                        <thead>
                            <tr>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    No
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Creator ID
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    File URL
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Upload Date
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Sipariş Notu
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    File Status
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {order?.assignedCreators.map((creator, index) => {
                                const creatorFiles = order.uploadFiles?.filter(
                                    file => file.uploadedBy === (creator as CreatorInterface)._id
                                );
                                console.log(`Creator ${index} files:`, creatorFiles);
                                console.log(`Creator ${index} ID:`, (creator as CreatorInterface)._id);
                                return creatorFiles && creatorFiles.length > 0 ? (
                                    creatorFiles.map((file, fileIndex) => {
                                        // Debug: Log the file object to see its structure
                                        console.log("File object:", file);
                                        console.log("File _Id:", file._Id);
                                        console.log("File keys:", Object.keys(file));

                                        // Try to get the file ID from different possible field names
                                        const fileId = file._Id || (file as any)._id || (file as any).id || (file as any).fileId;
                                        console.log("Resolved fileId:", fileId);
                                        console.log("File Status:", file.Status);
                                        return (
                                        <tr key={`${(creator as CreatorInterface)._id}-${fileIndex}`}>
                                            <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm'>
                                                {index + 1}
                                            </td>
                                            <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm'>
                                                {(creator as CreatorInterface)?._id}
                                            </td>
                                            <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border'>
                                                {file.fileUrls?.length > 0 ? (
                                                    file.fileUrls.map((f, i) => (
                                                        <div key={i} className="mb-2">
                                                            <a
                                                                className='text-xs lg:text-sm BlueText flex items-center gap-2'
                                                                href={f}
                                                                target='_blank'
                                                                rel='noopener noreferrer'
                                                            >
                                                                <span className="max-w-[200px] truncate">
                                                                    {f.split('/').pop() || f}
                                                                </span>
                                                                <svg
                                                                    className="w-3 h-3 flex-shrink-0"
                                                                    fill="currentColor"
                                                                    viewBox="0 0 20 20"
                                                                >
                                                                    <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                                                                    <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                                                                </svg>
                                                            </a>
                                                        </div>
                                                    ))
                                                ) : (
                                                    "No URL"
                                                )}
                                            </td>
                                            <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm text-gray-600'>
                                                {file?.uploadedDate
                                                    ? new Date(file.uploadedDate).toLocaleDateString()
                                                    : "No Date Available"}
                                            </td>
                                            <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm max-w-[200px] break-words'>
                                                {file.creatorNoteOnOrder || "No Notes"}
                                            </td>
                                            <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm'>
                                                {file.Status || "Pending"}
                                            </td>
                                            <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm flex gap-2'>
                                                <button
                                                    onClick={() => onMarkAsCompleted(order._id, fileId)}
                                                    className='text-green-500 hover:text-green-700'
                                                    disabled={!fileId}
                                                >
                                                    <FaCheck />
                                                </button>
                                                <button
                                                    onClick={() => onMarkAsRejected(order._id, fileId)}
                                                    className='text-red-500 hover:text-red-700'
                                                    disabled={!fileId}
                                                >
                                                    <FaTimes />
                                                </button>
                                            </td>
                                        </tr>
                                        );
                                    })
                                ) : (
                                    <tr key={`${(creator as CreatorInterface)._id}-no-files`}>
                                        <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm'>
                                            {index + 1}
                                        </td>
                                        <td className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm'>
                                            {(creator as CreatorInterface)?._id}
                                        </td>
                                        <td colSpan={5} className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-center'>
                                            No Files Uploaded
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                ) : (
                    <table className='text-xs lg:text-sm w-full bg-white table-auto'>
                        <thead>
                            <tr>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    No
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Creator ID
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    File URL
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Upload Date
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Sipariş Notu
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    File Status
                                </th>
                                <th className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 text-start border'>
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td
                                    colSpan={7}
                                    className='py-2 px-2 sm:py-2 sm:px-3 md:py-3 md:px-4 lg:py-3 lg:px-4 border text-xs lg:text-sm text-center'
                                >
                                    Henüz hiçbir Yaratıcı atanmadı
                                </td>
                            </tr>
                        </tbody>
                    </table>
                )}
            </div>
        </>
    );
};

export default ViewModal;