import PayTRForm from '@/app/payments/page';

interface Props {
  onClose: () => void;
}

export default function PayTRModal({ onClose }: Props) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-xl max-h-[95vh] overflow-y-auto relative px-6 pb-4 pt-0 custom-scrollbar">
        <button
          onClick={onClose}
          className="absolute top-4 right-2 text-gray-500 hover:text-black text-4xl"
        >
          ×
        </button>
        <PayTRForm />
      </div>
    </div>
  );
}
