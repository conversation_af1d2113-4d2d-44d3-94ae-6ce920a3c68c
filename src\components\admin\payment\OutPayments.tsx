"use client";
import { memo, useCallback, useEffect, useState, useMemo } from "react";
import { FaCheck, FaTimes, FaEye, FaFileCsv, FaSync , FaEdit } from "react-icons/fa";
import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import { fetchMyBrands } from "@/store/features/profile/brandSlice";
import { fetchOrders, updatePaymentStatus, updateOrderPaymentStatusLocally } from "@/store/features/admin/ordersSlice";
import CustomModelAdmin from "../../modal/CustomModelAdmin";
import ModalTwo from "./sub-in-payment/ViewInPaymentModal";
import { OrderInterface, CreatorInterface } from "@/types/interfaces";
import Image from "next/image";
import CustomTable from "@/components/custom-table/CustomTable";
import { updatePaymentNote } from "@/store/features/admin/ordersSlice";

// Interface for flattened order data with single creator
interface SingleCreatorOrderData {
    orderId: string;
    orderStatus: string;
    paymentStatus: string;
    paymentNote?: string;
    brandName?: string;
    brandImage?: string;
    ownerName?: string;
    ownerEmail?: string;
    creatorId: string;
    creatorName: string;
    creatorEmail?: string;
    priceForSingleCreator: number;
    originalOrder: OrderInterface;
}

interface TableActionsProps {
    onApprove: (id: string) => void;
    onReject: (id: string) => void;
    onView: (id: string) => void;
    onEdit: (id: string) => void;
    id: string;
}

const OutPayments: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [searchTerm, setSearchTerm] = useState("");
    const [isViewModalOpen, setIsViewModalOpen] = useState(false);
    const [selectedOrder, setSelectedOrder] = useState<OrderInterface | null>(null);
    const [isInitialLoading, setIsInitialLoading] = useState(true);

    // Payment Note Modal States
    const [noteModel, setNoteModel] = useState(false);
    const [selectedOrderId, setSelectedOrderId] = useState<string>("");
    const [paymentNote, setPaymentNote] = useState<string>("");
    const [isSavingNote, setIsSavingNote] = useState(false);

    // View Note Modal States
    const [viewNoteModal, setViewNoteModal] = useState(false);
    const [viewingNote, setViewingNote] = useState<string>("");



    const { data: orders, loading } = useSelector(
        (state: RootState) => state.orders
    );

    // Transform orders data to create separate entries for each creator
    const flattenedOrders = useMemo(() => {
        const result: SingleCreatorOrderData[] = [];

        // Only process Active and Completed orders
        const filteredOrders = orders.filter(
            (order: OrderInterface) =>
                order.orderStatus === "active" ||
                order.orderStatus === "completed"
        );

        filteredOrders.forEach((order: OrderInterface) => {
            // Check if assignedCreators exists and is an array
            if (Array.isArray(order.assignedCreators) && order.assignedCreators.length > 0) {
                // Use totalPriceForCreator divided by noOfUgc
                // This is the standard calculation method
                const pricePerCreator = order.totalPriceForCreator
                    ? order.totalPriceForCreator / order.noOfUgc
                    : 0;

                // Create a separate entry for each creator
                order.assignedCreators.forEach((creator: any) => {
                    result.push({
                        orderId: order._id,
                        orderStatus: order.orderStatus,
                        paymentStatus: order.paymentStatus,
                        paymentNote: order.paymentNote,
                        brandName: order.associatedBrands?.brandName,
                        brandImage: order.associatedBrands?.brandImage,
                        ownerName: order.orderOwner?.fullName,
                        ownerEmail: order.orderOwner?.email,
                        creatorId: typeof creator === 'string' ? creator : creator._id,
                        creatorName: typeof creator === 'string' ? 'Unknown' : creator.fullName,
                        creatorEmail: typeof creator === 'string' ? '' : creator.email,
                        priceForSingleCreator: pricePerCreator,
                        originalOrder: order
                    });
                });
            }
        });

        return result.sort((a, b) => {
            const dateA = new Date(a.originalOrder.createdAt).getTime();
            const dateB = new Date(b.originalOrder.createdAt).getTime();
            return dateB - dateA; // Newest first
        });
    }, [orders]);

    // Filter flattened orders based on search term
    const filteredFlattenedOrders = useMemo(() => {
        return flattenedOrders.filter(
            (item) =>
                item.brandName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.creatorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.creatorId.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [flattenedOrders, searchTerm]);

    const handleCloseModals = useCallback(() => {
        setIsViewModalOpen(false);
        setSelectedOrder(null);
    }, []);

    const handleView = useCallback(async (id: string) => {
        // Find the original order from the flattened data
        const item = flattenedOrders.find(item => item.orderId === id);
        if (item) {
            setSelectedOrder(item.originalOrder);
            setIsViewModalOpen(true);
        }
    }, [flattenedOrders]);

    const handleEdit = useCallback((id: string) => {
        // Find the order and get current payment note
        const item = flattenedOrders.find(item => item.orderId === id);
        if (item) {
            setSelectedOrderId(id);
            setPaymentNote(item.paymentNote || "");
            setNoteModel(true);
        }
    }, [flattenedOrders]);

    const handleSavePaymentNote = useCallback(async () => {
        if (!selectedOrderId) return;

        setIsSavingNote(true);
        try {
            console.log("💾 Saving payment note:", {
                orderId: selectedOrderId,
                paymentNote: paymentNote
            });

            // Call the API to update payment note
            await dispatch(updatePaymentNote({
                orderId: selectedOrderId,
                paymentNote
            })).unwrap();

            console.log("✅ Payment note saved successfully");

            // Close modal and reset state
            setIsSavingNote(false);
            setNoteModel(false);
            setSelectedOrderId("");
            setPaymentNote("");

            // Optionally refresh the orders data
            // dispatch(fetchOrders());

        } catch (error: any) {
            console.error("❌ Error saving payment note:", error);
            setIsSavingNote(false);
            // You might want to show an error toast here
            // toast.error(error.message || "Failed to save payment note");
        }
    }, [selectedOrderId, paymentNote, dispatch]);

    const handleCloseNoteModal = useCallback(() => {
        setNoteModel(false);
        setSelectedOrderId("");
        setPaymentNote("");
    }, []);

    const handleViewNote = useCallback((note: string) => {
        setViewingNote(note);
        setViewNoteModal(true);
    }, []);

    const handleCloseViewNoteModal = useCallback(() => {
        setViewNoteModal(false);
        setViewingNote("");
    }, []);

    const silentRefresh = useCallback(async () => {
        try {
            await dispatch(fetchOrders()).unwrap();
        } catch (error: any) {
            console.error("Silent refresh failed:", error);
        }
    }, [dispatch]);

    // Modified handleApprove with optimistic UI update
    const handleApprove = useCallback(async (id: string) => {
        try {
            dispatch(updateOrderPaymentStatusLocally({ orderId: id, paymentStatus: "approved" }));
            await dispatch(updatePaymentStatus({
                orderid: id,
                paymentstatus: "approved"
            })).unwrap();
            await dispatch(fetchOrders()).unwrap();
            toast.success("Payment Sent successfully!");
        } catch (error: any) {
            toast.error(error || "Failed to approve payment");
        }
    }, [dispatch]);

    // Modified handleReject with optimistic UI update
    const handleReject = useCallback(async (id: string) => {
        try {
            dispatch(updateOrderPaymentStatusLocally({ orderId: id, paymentStatus: "rejected" }));
            await dispatch(updatePaymentStatus({
                orderid: id,
                paymentstatus: "rejected"
            })).unwrap();
            await dispatch(fetchOrders()).unwrap();
            toast.dark("Payment rejected for the Order!");
        } catch (error: any) {
            toast.error(error || "Failed to reject payment");
        }
    }, [dispatch]);

    const TableActions = memo(
        ({ onApprove, onReject, onView, onEdit, id }: TableActionsProps) => (
            <div className='flex space-x-3'>
                <button
                    className='text-green-500 hover:text-green-700'
                    onClick={() => onApprove(id)}
                >
                    <FaCheck className='text-lg' />
                </button>
                <button
                    className='text-red-500 hover:text-red-700'
                    onClick={() => onReject(id)}
                >
                    <FaTimes className='text-lg' />
                </button>
                <button
                    className='text-gray-500 hover:text-gray-700'
                    onClick={() => onView(id)}
                >
                    <FaEye className='text-lg' />
                </button>
                <button
                    className='text-gray-500 hover:text-gray-700'
                    onClick={() => {
                        onEdit(id)
                        setNoteModel(true)
                    }}
                >
                    <FaEdit className='text-lg' />
                </button>
            </div>
        )
    );

    TableActions.displayName = "TableActions";

    const columns = [
        { name: "Order ID", selector: (row: SingleCreatorOrderData) => row.orderId, sortable: true },
        {
            name: "Order Title",
            cell: (row: SingleCreatorOrderData) => {
                return (
                    <div className='flex items-center space-x-2'>
                        <Image
                            width={100}
                            height={100}
                            src={
                                row.brandImage ||
                                "/icons/avatar.png"
                            }
                            alt='avatar'
                            className='w-10 h-10 rounded-full'
                        />
                        <div>
                            <p className='font-semibold'>
                                {row.brandName || "No Title"}
                            </p>
                            <p className='text-sm text-gray-500'>
                                {row.ownerEmail || "No Email"}
                            </p>
                        </div>
                    </div>
                );
            },
            sortable: false,
        },
        {
            name: "Creator Name",
            selector: (row: SingleCreatorOrderData) => row.creatorName,
            sortable: true,
        },
        {
            name: "Creator ID",
            selector: (row: SingleCreatorOrderData) => row.creatorId,
            sortable: true,
        },
        {
            name: "Order Status",
            selector: (row: SingleCreatorOrderData) => row.orderStatus,
            sortable: true,
        },
        {
            name: "Price For Single Creator",
            cell: (row: SingleCreatorOrderData) => (
                <span className="font-semibold">
                    {row.priceForSingleCreator.toLocaleString("tr-TR")} TL
                </span>
            ),
            sortable: true,
        },
        {
            name: "Payment Status",
            selector: (row: SingleCreatorOrderData) => row.paymentStatus,
            sortable: true,
            sortFunction: (a: SingleCreatorOrderData, b: SingleCreatorOrderData) => {
                const statusOrder = { pending: 0, approved: 1, rejected: 2 };
                return statusOrder[a.paymentStatus as keyof typeof statusOrder] - statusOrder[b.paymentStatus as keyof typeof statusOrder];
            },
        },
        {
            name: "Payment Note",
            selector: (row: SingleCreatorOrderData) => row.paymentNote || "No note",
            sortable: true,
            width: "200px", // Fixed width to prevent overflow
            cell: (row: SingleCreatorOrderData) => {
                const note = row.paymentNote || "No note";
                const maxLength = 30; // Maximum characters to show
                const isLongNote = note.length > maxLength;
                const truncatedNote = isLongNote ? note.substring(0, maxLength) + "..." : note;

                return (
                    <div className="flex items-center space-x-2 w-full">
                        <span
                            className="text-sm text-gray-700 flex-1 whitespace-nowrap overflow-hidden text-ellipsis"
                            title={note}
                        >
                            {truncatedNote}
                        </span>
                        {isLongNote && (
                            <button
                                onClick={() => handleViewNote(note)}
                                className="text-blue-500 hover:text-blue-700 text-xs px-2 py-1 rounded border border-blue-300 hover:border-blue-500 transition-colors whitespace-nowrap"
                                title="View full note"
                            >
                                View
                            </button>
                        )}
                    </div>
                );
            },
            sortFunction: (a: SingleCreatorOrderData, b: SingleCreatorOrderData) => {
                const noteA = a.paymentNote || "";
                const noteB = b.paymentNote || "";
                return noteA.localeCompare(noteB);
            },
        },
        {
            name: "Actions",
            cell: (row: SingleCreatorOrderData) => (
                <TableActions
                    onApprove={handleApprove}
                    onReject={handleReject}
                    onView={handleView}
                    onEdit={handleEdit}
                    id={row.orderId}
                />
            ),
            width: "200px",
        },
    ];

    const exportToCSV = () => {
        const csvRows = [
            ["Order ID", "Creator Name", "Creator ID", "Order Status", "Price For Single Creator", "Payment Status", "Payment Note"],
            ...filteredFlattenedOrders.map((item: SingleCreatorOrderData) => [
                item.orderId,
                item.creatorName,
                item.creatorId,
                item.orderStatus,
                item.priceForSingleCreator,
                item.paymentStatus,
                item.paymentNote || "No note",
            ]),
        ];
        const csvContent =
            "data:text/csv;charset=utf-8," +
            csvRows.map((e) => e.join(",")).join("\n");
        const link = document.createElement("a");
        link.setAttribute("href", encodeURI(csvContent));
        link.setAttribute("download", "outgoing-payments-per-creator.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const fetchOrdersData = useCallback(async () => {
        try {
            await dispatch(fetchOrders()).unwrap();
            setIsInitialLoading(false);
            toast.success("Orders data refreshed successfully");
        } catch (error: any) {
            setIsInitialLoading(false);
            toast.error(error.message || "Failed to fetch orders");
        }
    }, [dispatch]);

    const fetchBrands = useCallback(async () => {
        try {
            await dispatch(fetchMyBrands()).unwrap();
        } catch (error: any) {
            toast.error(error.message || "Failed to fetch brands");
        }
    }, [dispatch]);

    useEffect(() => {
        fetchBrands();
        fetchOrdersData();
    }, [fetchBrands, fetchOrdersData]);

    // Use initial loading state only for first load, not for payment updates
    const tableLoading = isInitialLoading && loading;

    return (
        <div className='bg-white rounded-lg'>
            <div className='flex flex-col py-24 md:py-24 lg:my-0 px-4 sm:px-6 md:px-12 lg:pl-72'>
                {/* Search and Buttons */}
                <div className='flex flex-col sm:flex-row justify-between items-stretch sm:items-center mb-4 space-y-2 sm:space-y-0 sm:space-x-2'>
                    <div className='flex justify-center items-center w-full sm:w-auto'>
                        <input
                            type='text'
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder='Search...'
                            className='p-2 border border-gray-300 rounded-lg w-full sm:w-auto'
                        />
                    </div>
                    <div className='flex flex-col xs:flex-row justify-center space-y-2 xs:space-y-0 xs:space-x-2 w-full sm:w-auto'>
                        <button
                            className='px-4 py-2 bg-blue-500 text-white rounded-md w-full xs:w-auto'
                            onClick={fetchOrdersData}
                        >
                            Refresh <FaSync className='inline ml-2' />
                        </button>
                        <button
                            className='px-4 py-2 Button text-white rounded-md w-full xs:w-auto'
                        >
                            Add Out Payment
                        </button>
                        <button
                            className='px-4 py-2 bg-green-500 text-white rounded-md w-full xs:w-auto'
                            onClick={exportToCSV}
                        >
                            Export CSV <FaFileCsv className='inline ml-2' />
                        </button>
                    </div>
                </div>
                {/* Data Table */}
                <div className='shadow-md'>
                    <CustomTable
                        columns={columns}
                        data={filteredFlattenedOrders}
                        noDataComponent='No Outgoing Payments Found'
                        loading={tableLoading}
                    />
                </div>
            </div>

            {/* Modal */}
            <CustomModelAdmin
                isOpen={isViewModalOpen}
                closeModal={handleCloseModals}
                title='Order Details'
            >
                {selectedOrder && (
                    <div className="p-4">
                        <h2 className="text-xl font-bold mb-4">Order Information</h2>
                        <div className="grid grid-cols-2 gap-4 mb-6">
                            <div>
                                <p className="font-semibold">Order ID:</p>
                                <p>{selectedOrder._id}</p>
                            </div>
                            <div>
                                <p className="font-semibold">Order Status:</p>
                                <p>{selectedOrder.orderStatus}</p>
                            </div>
                            <div>
                                <p className="font-semibold">Payment Status:</p>
                                <p>{selectedOrder.paymentStatus}</p>
                            </div>
                            <div>
                                <p className="font-semibold">Payment Note:</p>
                                <p>{selectedOrder.paymentNote}</p>
                            </div>
                            <div>
                                <p className="font-semibold">Number of UGCs:</p>
                                <p>{selectedOrder.noOfUgc}</p>
                            </div>
                            <div>
                                <p className="font-semibold">Total Price for Creator:</p>
                                <p>{selectedOrder.totalPriceForCreator?.toLocaleString("tr-TR")} TL</p>
                            </div>
                            <div>
                                <p className="font-semibold">Price Per Creator:</p>
                                <p>{selectedOrder.totalPriceForCreator && selectedOrder.noOfUgc
                                    ? (selectedOrder.totalPriceForCreator / selectedOrder.noOfUgc).toLocaleString("tr-TR")
                                    : 0} TL</p>
                            </div>
                        </div>

                        <h3 className="text-lg font-bold mb-2">Assigned Creators</h3>
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white border">
                                <thead>
                                    <tr>
                                        <th className="py-2 px-4 border">Creator ID</th>
                                        <th className="py-2 px-4 border">Creator Name</th>
                                        <th className="py-2 px-4 border">Creator Email</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {Array.isArray(selectedOrder.assignedCreators) &&
                                        selectedOrder.assignedCreators.map((creator: any, index: number) => (
                                            <tr key={index}>
                                                <td className="py-2 px-4 border">{typeof creator === 'string' ? creator : creator._id}</td>
                                                <td className="py-2 px-4 border">{typeof creator === 'string' ? 'Unknown' : creator.fullName}</td>
                                                <td className="py-2 px-4 border">{typeof creator === 'string' ? '' : creator.email}</td>
                                            </tr>
                                        ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                )}
            </CustomModelAdmin>

            {/* Payment Note Modal */}
            {noteModel && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
                        {/* Modal Header */}
                        <div className="flex items-center justify-between p-6 border-b">
                            <h2 className="text-xl font-semibold text-gray-800">
                                Edit Payment Note
                            </h2>
                            <button
                                onClick={handleCloseNoteModal}
                                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                                disabled={isSavingNote}
                            >
                                <FaTimes size={16} />
                            </button>
                        </div>

                        {/* Modal Body */}
                        <div className="p-6">
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Payment Note
                                </label>
                                <textarea
                                    value={paymentNote}
                                    onChange={(e) => setPaymentNote(e.target.value)}
                                    placeholder="Enter payment note..."
                                    rows={4}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                    disabled={isSavingNote}
                                />
                            </div>
                            <div className="text-sm text-gray-500">
                                Order ID: {selectedOrderId}
                            </div>
                        </div>

                        {/* Modal Footer */}
                        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
                            <button
                                onClick={handleCloseNoteModal}
                                className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                disabled={isSavingNote}
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleSavePaymentNote}
                                disabled={isSavingNote}
                                className={`px-4 py-2 rounded-md transition-colors ${
                                    isSavingNote
                                        ? 'bg-gray-400 cursor-not-allowed text-white'
                                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                                }`}
                            >
                                {isSavingNote ? (
                                    <div className="flex items-center">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Saving...
                                    </div>
                                ) : (
                                    'Save Note'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* View Note Modal */}
            {viewNoteModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
                        {/* Modal Header */}
                        <div className="flex items-center justify-between p-6 border-b">
                            <h2 className="text-xl font-semibold text-gray-800">
                                Payment Note Details
                            </h2>
                            <button
                                onClick={handleCloseViewNoteModal}
                                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                            >
                                <FaTimes size={16} />
                            </button>
                        </div>

                        {/* Modal Body */}
                        <div className="p-6">
                            <div className="bg-gray-50 rounded-lg p-4 border">
                                <p className="text-gray-800 whitespace-pre-wrap leading-relaxed">
                                    {viewingNote || "No note available"}
                                </p>
                            </div>
                        </div>

                        {/* Modal Footer */}
                        <div className="flex items-center justify-end p-6 border-t bg-gray-50">
                            <button
                                onClick={handleCloseViewNoteModal}
                                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default OutPayments;