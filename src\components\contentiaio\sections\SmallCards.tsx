"use client";

import SmallCard from "@/components/customCard/SmallCard";
import React from "react";
import Slider from "react-slick";

const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
};

const CARDS = [
    {
        image: "/SCIMG1.png",
        title: "Doğrudan Performans Etkisi",
        description:
            "Dijital pazarlama süreçlerini gerçek kullanıcılar tarafından içeriklerle destekleyerek hızlı sonuçlar elde edin. Pazarlama maliyetlerini düşürün ve kullanıcıların ürettiği içeriklerle bilinirliğinizi organik olarak artırın.",
    },
    {
        image: "/SCIMG2.png",
        title: "Reklam Algısını Baştan Yaratın",
        description:
            "Ürününüzü ya da bir hizmetinizi satın alan kişiler tarafından oluşturulan içerikler, di<PERSON><PERSON> kullanıc<PERSON>lar için daha güvenilirdir. Bu sebeple insanlar, markaların kendi reklamlarına oranla diğer müşterilerin deneyimlerine dayanan içerikleri daha samimi bulur.",
    },
    {
        image: "/SCIMG3.png",
        title: "Birkaç Dakikada Sipariş Oluşturun",
        description:
            "İçeriklerinizi planlayın ve birkaç dakikada sipariş oluşturun. Profesyonel içerik üretim maliyetlerine göre daha uygun fiyatlarla UGC’lere sahip olun. Siparişlerinizi kolayca yönetin.",
    },
    {
        image: "/SCIMG4.png",
        title: "Kişiselleştirilmiş İçerik",
        description:
            "Marka kimliğinize, ürün detaylarınıza, sosyal medyanıza ve hedeflerinize uygun kişiselleştirilmiş içeriklere erişin. Markanızın sürekli güncel kalması için devamlı olarak yeni içerik ihtiyacını karşılayın ve sosyal medya kanallarını dinamik tutun.",
    },
    {
        image: "/SCIMG5.png",
        title: "Geniş Influencer Ağı",
        description:
            "Platformumuz tarafından seçilmiş güvenilir içerik üreticilerle çalışın. İçerik ihtiyacınıza göre, nano, mikro ve makro influencerlarla birlikte sosyal medya trendlerini yakalayın ve sosyal medya pazarlamasında viral kampanyalar yaratın.",
    },
    {
        image: "/SCIMG6.png",
        title: "Bütçe Dostu ve Ölçeklenebilir",
        description:
            "Sosyal medyanızı uygun fiyatlarla dinamik ve samimi şekilde yönetin, reklam ve tanıtımlarınızı ölçeklendirerek tıklanma başına maliyetlerinizi düşürün ve topluluk etkisi yaratın.",
    },
];

export default function SmallCards() {
    return (
        <div className='w-full max-w-full overflow-hidden ml-2 mt-10 sm:mt-10 md:mt-16 lg:mt-20'>
            <div className='overflow-hidden'>
                <div className='block lg:hidden overflow-hidden'>
                    <Slider {...settings}>
                        {CARDS.map((card, index) => (
                            <div
                                key={index}
                                className='px-2 py-4'
                            >
                                {" "}
                                {/* Adds padding around each card */}
                                <SmallCard
                                    image={card.image}
                                    title={card.title}
                                    description={card.description}
                                />
                            </div>
                        ))}
                    </Slider>
                </div>

                {/* Grid on lg screens */}
                <div className='hidden lg:grid lg:grid-cols-3 gap-6'>
                    {CARDS.map((card, index) => (
                        <SmallCard
                            key={index}
                            image={card.image}
                            title={card.title}
                            description={card.description}
                        />
                    ))}
                </div>

                <div className='flex justify-center items-center mt-8'>
                    <div>
                        <button className='Button text-white font-bold py-2 px-8 rounded focus:outline-none focus:shadow-outline'>
                            <a href='#fiyatlandırma'>Fi​yatlar </a>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
