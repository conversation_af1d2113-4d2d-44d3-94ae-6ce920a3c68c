"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { changeProfilePicture } from "@/store/features/profile/profileSlice";
import { RootState, AppDispatch } from "@/store/store";
import { toast } from "react-toastify";

interface LogoUploaderProps {
    currentImage?: string | null;
}

export default function LogoUploader({ currentImage }: LogoUploaderProps) {
    const dispatch = useDispatch<AppDispatch>();
    const profile = useSelector((state: RootState) => state.profile);
    const { register, setValue } = useForm();
    const [previewImage, setPreviewImage] = useState<string | null>(
        currentImage || profile.data?.profilePic || null
    );
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState(false);
    const [pendingFile, setPendingFile] = useState<File | null>(null);

    useEffect(() => {
        if (currentImage) {
            setPreviewImage(currentImage);
        } else if (profile.data?.profilePic) {
            setPreviewImage(profile.data.profilePic);
        }
    }, [currentImage, profile.data?.profilePic]);

    const uploadProfilePicture = async (file: File) => {
        setIsUploading(true);
        setUploadError(false);

        try {
            console.log("🔄 Uploading profile picture...");
            await dispatch(changeProfilePicture({ file })).unwrap();
            toast.success("Profil fotoğrafı başarıyla güncellendi!");
            setValue("profilePic", file);
            setPendingFile(null); // Clear pending file on success
        } catch (error: any) {
            console.error("❌ Error updating profile picture:", error);

            setUploadError(true);
            setPendingFile(file); // Store file for retry

            // Show specific error message
            let errorMessage = "Profil fotoğrafı güncellenirken bir hata oluştu.";
            if (typeof error === 'string') {
                errorMessage = error;
            } else if (error?.message) {
                errorMessage = error.message;
            }

            toast.error(errorMessage);
        } finally {
            setIsUploading(false);
        }
    };

    const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        e.target.value = ""; // reset the input value after file is selected

        if (!file) {
            toast.error("Görüntü seçilmedi. Lütfen yüklemek için bir görüntü seçin.");
            return;
        }

        // Show preview immediately
        const reader = new FileReader();
        reader.onloadend = () => {
            setPreviewImage(reader.result as string);
        };
        reader.readAsDataURL(file);

        // Upload to database
        await uploadProfilePicture(file);
    };

    const handleRetry = () => {
        if (pendingFile) {
            uploadProfilePicture(pendingFile);
        }
    };

    return (
        <div className='w-full'>
            <div
                className='relative rounded-md p-4 text-center'
                style={{ width: "150px" }}
            >
                <label
                    htmlFor='profilePic'
                    className={`cursor-pointer block w-full h-full ${isUploading ? 'pointer-events-none' : ''}`}
                >
                    <input
                        id='profilePic'
                        type='file'
                        accept='image/*'
                        className='hidden'
                        {...register("profilePic")}
                        onChange={handleImageChange}
                        disabled={isUploading}
                    />

                    <div className='relative'>
                        {previewImage ? (
                            <img
                                src={previewImage}
                                alt='Preview'
                                className={`w-full h-full object-cover rounded-md transition-opacity duration-300 ${
                                    isUploading ? 'opacity-50' : 'opacity-100'
                                }`}
                            />
                        ) : (
                            <div className='w-28 h-28 bg-gray-300 text-white rounded-full flex items-center justify-center mx-auto'>
                                Profile
                            </div>
                        )}

                        {/* Loading Overlay */}
                        {isUploading && (
                            <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-md'>
                                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-white'></div>
                            </div>
                        )}

                        {/* Error Retry Button */}
                        {uploadError && !isUploading && (
                            <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-md'>
                                <button
                                    onClick={handleRetry}
                                    className='bg-red-500 hover:bg-red-600 text-white p-2 rounded-full transition-colors duration-200'
                                    title='Retry upload'
                                >
                                    <svg
                                        className='w-5 h-5'
                                        fill='none'
                                        stroke='currentColor'
                                        viewBox='0 0 24 24'
                                    >
                                        <path
                                            strokeLinecap='round'
                                            strokeLinejoin='round'
                                            strokeWidth={2}
                                            d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                                        />
                                    </svg>
                                </button>
                            </div>
                        )}
                    </div>
                </label>

                {/* Upload Status Text */}
                {isUploading && (
                    <p className='text-sm text-blue-600 mt-2 font-medium'>
                        Yükleniyor...
                    </p>
                )}

                {uploadError && !isUploading && (
                    <p className='text-sm text-red-600 mt-2 font-medium'>
                        Yükleme başarısız. Tekrar deneyin.
                    </p>
                )}
            </div>
        </div>
    );
}
